import { createUnarySpacing } from '../spacing';

// The different signatures imply different meaning for their arguments that can't be expressed structurally.
// We express the difference with variable names.

export default function createSpacing() {
  var spacingInput = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 8;
  // Already transformed.
  if (spacingInput.mui) {
    return spacingInput;
  }

  // Material Design layouts are visually balanced. Most measurements align to an 8dp grid, which aligns both spacing and the overall layout.
  // Smaller components, such as icons, can align to a 4dp grid.
  // https://m2.material.io/design/layout/understanding-layout.html
  var transform = createUnarySpacing({
    spacing: spacingInput
  });
  var spacing = function spacing() {
    for (var _len = arguments.length, argsInput = new Array(_len), _key = 0; _key < _len; _key++) {
      argsInput[_key] = arguments[_key];
    }
    if (process.env.NODE_ENV !== 'production') {
      if (!(argsInput.length <= 4)) {
        console.error("MUI: Too many arguments provided, expected between 0 and 4, got ".concat(argsInput.length));
      }
    }
    var args = argsInput.length === 0 ? [1] : argsInput;
    return args.map(function (argument) {
      var output = transform(argument);
      return typeof output === 'number' ? "".concat(output, "px") : output;
    }).join(' ');
  };
  spacing.mui = true;
  return spacing;
}