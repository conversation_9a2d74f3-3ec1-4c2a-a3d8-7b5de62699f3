"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _deepmerge = _interopRequireDefault(require("@mui/utils/deepmerge"));
var _createBreakpoints = _interopRequireDefault(require("./createBreakpoints"));
var _shape = _interopRequireDefault(require("./shape"));
var _createSpacing = _interopRequireDefault(require("./createSpacing"));
var _styleFunctionSx = _interopRequireDefault(require("../styleFunctionSx/styleFunctionSx"));
var _defaultSxConfig = _interopRequireDefault(require("../styleFunctionSx/defaultSxConfig"));
var _applyStyles = _interopRequireDefault(require("./applyStyles"));
const _excluded = ["breakpoints", "palette", "spacing", "shape"];
function createTheme(options = {}, ...args) {
  const {
      breakpoints: breakpointsInput = {},
      palette: paletteInput = {},
      spacing: spacingInput,
      shape: shapeInput = {}
    } = options,
    other = (0, _objectWithoutPropertiesLoose2.default)(options, _excluded);
  const breakpoints = (0, _createBreakpoints.default)(breakpointsInput);
  const spacing = (0, _createSpacing.default)(spacingInput);
  let muiTheme = (0, _deepmerge.default)({
    breakpoints,
    direction: 'ltr',
    components: {},
    // Inject component definitions.
    palette: (0, _extends2.default)({
      mode: 'light'
    }, paletteInput),
    spacing,
    shape: (0, _extends2.default)({}, _shape.default, shapeInput)
  }, other);
  muiTheme.applyStyles = _applyStyles.default;
  muiTheme = args.reduce((acc, argument) => (0, _deepmerge.default)(acc, argument), muiTheme);
  muiTheme.unstable_sxConfig = (0, _extends2.default)({}, _defaultSxConfig.default, other == null ? void 0 : other.unstable_sxConfig);
  muiTheme.unstable_sx = function sx(props) {
    return (0, _styleFunctionSx.default)({
      sx: props,
      theme: this
    });
  };
  return muiTheme;
}
var _default = exports.default = createTheme;