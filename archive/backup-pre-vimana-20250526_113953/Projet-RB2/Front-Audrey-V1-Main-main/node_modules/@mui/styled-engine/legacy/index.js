/**
 * @mui/styled-engine v5.16.14
 *
 * @license MIT
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use client';

/* eslint-disable no-underscore-dangle */
import emStyled from '@emotion/styled';
export default function styled(tag, options) {
  var stylesFactory = emStyled(tag, options);
  if (process.env.NODE_ENV !== 'production') {
    return function () {
      var component = typeof tag === 'string' ? "\"".concat(tag, "\"") : 'component';
      for (var _len = arguments.length, styles = new Array(_len), _key = 0; _key < _len; _key++) {
        styles[_key] = arguments[_key];
      }
      if (styles.length === 0) {
        console.error(["MUI: Seems like you called `styled(".concat(component, ")()` without a `style` argument."), 'You must provide a `styles` argument: `styled("div")(styleYouForgotToPass)`.'].join('\n'));
      } else if (styles.some(function (style) {
        return style === undefined;
      })) {
        console.error("MUI: the styled(".concat(component, ")(...args) API requires all its args to be defined."));
      }
      return stylesFactory.apply(void 0, styles);
    };
  }
  return stylesFactory;
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export var internal_processStyles = function internal_processStyles(tag, processor) {
  // Emotion attaches all the styles as `__emotion_styles`.
  // Ref: https://github.com/emotion-js/emotion/blob/16d971d0da229596d6bcc39d282ba9753c9ee7cf/packages/styled/src/base.js#L186
  if (Array.isArray(tag.__emotion_styles)) {
    tag.__emotion_styles = processor(tag.__emotion_styles);
  }
};
export { ThemeContext, keyframes, css } from '@emotion/react';
export { default as StyledEngineProvider } from './StyledEngineProvider';
export { default as GlobalStyles } from './GlobalStyles';