/**
 * @typedef {Object} WDSMetaObj
 * @property {boolean} enforceWs
 * @property {number} version
 */

/**
 * Derives WDS metadata from a compatible socket client.
 * @param {Function} SocketClient A WDS socket client (SockJS/WebSocket).
 * @returns {WDSMetaObj} The parsed WDS metadata object.
 */
function getWDSMetadata(SocketClient) {
  let enforceWs = false;
  if (
    typeof SocketClient.name !== 'undefined' &&
    SocketClient.name !== null &&
    SocketClient.name.toLowerCase().includes('websocket')
  ) {
    enforceWs = true;
  }

  let version;
  // WDS versions <=3.5.0
  if (!('onMessage' in SocketClient.prototype)) {
    version = 3;
  } else {
    // WDS versions >=3.5.0 <4
    if (
      'getClientPath' in SocketClient ||
      Object.getPrototypeOf(SocketClient).name === 'BaseClient'
    ) {
      version = 3;
    } else {
      version = 4;
    }
  }

  return {
    enforceWs: enforceWs,
    version: version,
  };
}

module.exports = getWDSMetadata;
