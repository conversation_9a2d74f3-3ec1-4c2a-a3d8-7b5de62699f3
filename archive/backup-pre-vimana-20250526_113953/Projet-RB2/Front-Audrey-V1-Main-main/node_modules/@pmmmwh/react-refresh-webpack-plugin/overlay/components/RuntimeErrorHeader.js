const Spacer = require('./Spacer.js');
const theme = require('../theme.js');

/**
 * @typedef {Object} RuntimeErrorHeaderProps
 * @property {number} currentErrorIndex
 * @property {number} totalErrors
 */

/**
 * A fixed header that shows the total runtime error count.
 * @param {Document} document
 * @param {HTMLElement} root
 * @param {RuntimeErrorHeaderProps} props
 * @returns {void}
 */
function RuntimeErrorHeader(document, root, props) {
  const header = document.createElement('div');
  header.innerText = 'Error ' + (props.currentErrorIndex + 1) + ' of ' + props.totalErrors;
  header.style.backgroundColor = '#' + theme.red;
  header.style.color = '#' + theme.white;
  header.style.fontWeight = '500';
  header.style.height = '2.5rem';
  header.style.left = '0';
  header.style.lineHeight = '2.5rem';
  header.style.position = 'fixed';
  header.style.textAlign = 'center';
  header.style.top = '0';
  header.style.width = '100vw';
  header.style.zIndex = '2';

  root.appendChild(header);

  Spacer(document, root, { space: '2.5rem' });
}

module.exports = RuntimeErrorHeader;
